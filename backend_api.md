# Nepali App API Documentation

**Base URL:** `http://napp-api.nextai.asia:8204`
**Version:** 1.0.0
**Last Updated:** December 2024

## Table of Contents

1. [Authentication](#authentication)
2. [Auth Service API](#auth-service-api)
3. [Socket Service API](#socket-service-api)
4. [Management Service API](#management-service-api)
5. [Error Handling](#error-handling)
6. [Status Codes](#status-codes)

---

## Authentication

All protected endpoints require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

### Getting a JWT Token

1. **Register/Login** via Auth Service endpoints
2. **Include the token** in subsequent API requests
3. **Token expires** after 2 hours - use refresh endpoints if available

---

## Auth Service API

**Base Path:** `/auth`

### Authentication Endpoints

#### POST /signup
Register a new user with email and password.

**Request Body (Form Data or JSON):**
```json
{
  "username": "string",        // required
  "email": "<EMAIL>", // required
  "password": "string",        // required
  "client_id": "string",       // required - tenant client ID
  "full_name": "string",       // optional
  "phone_number": "string",    // optional
  "country_code": "+1"         // optional - required if phone_number provided
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "password",
    "onboarding_completed": false
  }
}
```

**Error Responses:**
- `400 Bad Request`: Missing required fields or user already exists
- `500 Internal Server Error`: Server error during registration

#### POST /login
Authenticate user with username/email and password.

**Request Body (Form Data or JSON):**
```json
{
  "username": "string",     // required - username or email
  "password": "string",     // required
  "client_id": "string"     // required - tenant client ID
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "password",
    "last_login": "2024-01-01T10:00:00Z",
    "previous_login": "2024-01-01T09:00:00Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Missing required fields
- `401 Unauthorized`: Invalid credentials
- `500 Internal Server Error`: Server error during login

#### POST /google-auth
Authenticate user with Google ID token.

**Request Body (Form Data or JSON):**
```json
{
  "id_token": "string",     // required - Google ID token
  "client_id": "string"     // required - tenant client ID
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "string",
    "auth_provider": "google",
    "google_id": "google_user_id",
    "profile_picture": "https://..."
  }
}
```

#### GET /verify_token
Verify JWT token and return user details.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "valid": true,
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "<EMAIL>",
    "role": "user",
    "tenant_id": "tenant_id"
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or expired token

#### GET /get_tenant_id
Get tenant ID from slug.

**Query Parameters:**
- `slug` (string, required): Tenant slug

**Response (200 OK):**
```json
{
  "tenant_id": "string",
  "tenant_name": "string"
}
```

#### GET /oauth-config
Get OAuth configuration for frontend use.

**Response (200 OK):**
```json
{
  "google_client_id": "string",
  "redirect_uri": "http://localhost:3000/auth/google/callback"
}
```

### User Management Endpoints

#### POST /onboarding
User onboarding for profile setup after signup.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "age": 7,                                    // required
  "difficulty_level": 2,                 // required - {"easy":1, "medium":2, "hard":3}
  "preferred_topics": ["math", "science"]     // optional
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Onboarding completed successfully",
  "user_id": "string",
  "personalization_ready": true
}
```

#### POST /users/change_password
Change user password.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "old_password": "string",     // required
  "new_password": "string"      // required
}
```

**Response (200 OK):**
```json
{
  "message": "Password successfully changed."
}
```

**Error Responses:**
- `400 Bad Request`: Current password incorrect or new password same as current
- `404 Not Found`: User not found

#### POST /users/reset_password
Reset user password (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "username": "string",        // required
  "new_password": "string"     // optional - auto-generated if not provided
}
```

**Response (200 OK):**
```json
{
  "message": "Password reset successfully.",
  "new_password": "string"     // if auto-generated
}
```

**Authentication:** Requires admin role

#### POST /users/invite
Invite a new user (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "username": "string",     // required
  "role": "user"           // required
}
```

**Response (200 OK):**
```json
{
  "registration_token": "string",
  "success": true,
  "msg": "Token Generated!"
}
```

**Authentication:** Requires admin role

#### POST /users/register
Register a new user with invitation token.

**Request Body:**
```json
{
  "token": "string",        // required - invitation token
  "password": "string"      // required
}
```

**Response (200 OK):**
```json
{
  "message": "Agent registered successfully.",
  "username": "string"
}
```

#### GET /users/{user_id}
Get user details by ID (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `user_id` (string, required): User ID

**Response (200 OK):**
```json
{
  "_id": "string",
  "username": "string",
  "role": "string"
}
```

**Authentication:** Requires admin role

### Role Management Endpoints

#### GET /roles/
Get all roles with pagination (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 10, max: 100): Items per page

**Response (200 OK):**
```json
{
  "data": [
    {
      "_id": "string",
      "name": "string"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

**Authentication:** Requires admin role

#### POST /roles/
Create a new role (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "name": "string"     // required
}
```

**Response (200 OK):**
```json
{
  "_id": "string",
  "name": "string"
}
```

**Authentication:** Requires admin role

#### DELETE /roles/{role_id}
Delete a role (admin only).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `role_id` (string, required): Role ID

**Response (200 OK):**
```json
{
  "message": "Role deleted successfully."
}
```

**Error Responses:**
- `400 Bad Request`: Role is assigned to users
- `404 Not Found`: Role not found

**Authentication:** Requires admin role

### Health Check

#### GET /health
Service health check.

**Response (200 OK):**
```json
{
  "status": "healthy"
}
```

## Socket Service API

**Base Path:** `/socket`

The Socket Service handles real-time Socket.IO communication for audio streaming and task generation.

### Socket Authentication Endpoints

#### POST /connect
Create authenticated Socket.IO session for real-time communication.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "difficulty": "easy",        // optional - "easy", "medium", "hard" (default: "easy")
  "num_tasks": 3,             // optional - number of tasks to generate (default: 3)
  "chunk_threshold": 20,      // optional - audio chunks before processing (default: 20)
  "metadata": {}              // optional - additional metadata
}
```

**Response (200 OK):**
```json
{
  "session_token": "string",
  "session_id": "string",
  "websocket_url": "/socket/socket.io",
  "expires_at": "2024-01-01T12:00:00Z",
  "configuration": {
    "difficulty": "easy",
    "num_tasks": 3,
    "chunk_threshold": 20
  },
  "status": "ready",
  "instructions": {
    "next_step": "Connect to WebSocket using session_token",
    "websocket_endpoint": "/socket/socket.io",
    "auth_method": "Include session_token in auth object",
    "flow": {
      "1": "Send 'stream_starting' event to begin",
      "2": "Wait for 'stream_starting_ack' response",
      "3": "Send binary audio chunks",
      "4": "Send 'stream_completed' or 'stream_stop' to finish"
    },
    "events": {
      "stream_starting": "Begin audio streaming session",
      "binary_data": "Send audio chunks for processing",
      "stream_completed": "Signal normal completion",
      "stream_stop": "Signal forced stop"
    }
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid JWT token
- `500 Internal Server Error`: Failed to create session

#### GET /validate/{session_token}
Validate Socket.IO session token (internal use).

**Path Parameters:**
- `session_token` (string, required): Session token to validate

**Response (200 OK):**
```json
{
  "valid": true,
  "session_id": "string",
  "user_id": "string",
  "tenant_id": "string",
  "configuration": {
    "difficulty": "easy",
    "num_tasks": 3,
    "chunk_threshold": 20
  }
}
```

**Error Responses:**
- `404 Not Found`: Session not found
- `401 Unauthorized`: Session expired

#### DELETE /session/{session_token}
Clean up Socket.IO session.

**Path Parameters:**
- `session_token` (string, required): Session token to clean up

**Response (200 OK):**
```json
{
  "message": "Session cleaned up successfully"
}
```

#### PUT /session/{session_token}/status
Update session status.

**Path Parameters:**
- `session_token` (string, required): Session token

**Request Body:**
```json
{
  "session_id": "string",     // required
  "status": "STARTED",        // required - "STARTED", "ACTIVE", "CANCELLED", "COMPLETED"
  "timestamp": "2024-01-01T10:00:00Z",
  "metadata": {}              // optional
}
```

**Response (200 OK):**
```json
{
  "message": "Session status updated successfully"
}
```

#### GET /status
Get Socket.IO connection status and statistics.

**Response (200 OK):**
```json
{
  "active_sessions": 5,
  "expired_sessions": 2,
  "total_sessions": 7,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### WebSocket Connection

#### Socket.IO Endpoint: /socket.io
Real-time WebSocket communication endpoint.

**Connection:**
```javascript
import io from 'socket.io-client';

const socket = io('/socket/socket.io', {
  auth: {
    session_token: 'your_session_token_from_connect_endpoint'
  }
});
```

### Socket.IO Events

#### Client → Server Events

##### stream_starting
Begin audio streaming session.

**Event Data:**
```json
{
  "session_id": "string"     // required - session ID from /connect response
}
```

##### binary_data
Send audio chunks for processing.

**Event Data:**
- Binary audio data (ArrayBuffer/Blob)

**Metadata:**
```json
{
  "session_id": "string",    // required
  "chunk_index": 1,          // required - sequential chunk number
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_completed
Signal normal completion of audio streaming.

**Event Data:**
```json
{
  "session_id": "string"     // required
}
```

##### stream_stop
Signal forced stop of audio streaming.

**Event Data:**
```json
{
  "session_id": "string"     // required
}
```

#### Server → Client Events

##### stream_starting_ack
Acknowledgment that streaming has started.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "streaming_active",
  "buffer_status": {
    "chunks_received": 0,
    "chunks_required": 20
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_completed_ack
Acknowledgment of stream completion.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "completed",
  "total_chunks_processed": 45,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_stop_ack
Acknowledgment of forced stop.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "stopped",
  "partial_results": {
    "tasks_generated": 3,
    "task_set_id": "ts_789_partial",
    "can_resume": false
  },
  "cleanup_completed": true,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_processing
Notification that task generation is in progress.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "processing",
  "progress": {
    "stage": "audio_analysis",
    "completion_percentage": 45,
    "estimated_time_remaining": "30s"
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_complete
Notification that task generation is complete.

**Event Data:**
```json
{
  "session_id": "string",
  "status": "completed",
  "results": {
    "task_set_id": "string",
    "tasks_generated": 5,
    "total_score": 50,
    "difficulty": "easy"
  },
  "next_steps": {
    "access_tasks": "/management/task-sets/{task_set_id}",
    "start_quiz": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### task_generation_failed
Notification that task generation has failed.

**Event Data:**
```json
{
  "session_id": "string",
  "message": "No tasks generated from the provided input",
  "timestamp": "2024-01-01T12:00:00Z",
  "tasks": [],
  "task_count": 0,
  "status": "failed",
  "error": "No tasks generated from the provided input"
}
```

##### task_generation_cancelled
Notification that task generation was cancelled.

**Event Data:**
```json
{
  "session_id": "string",
  "cancellation_reason": "timeout",
  "timeout_duration": "300s",
  "partial_results": {
    "tasks_generated": 1,
    "processing_stage": "task_generation",
    "can_recover": false
  },
  "next_steps": {
    "restart_session": true,
    "retry_recommended": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

##### stream_error
Error during streaming or processing.

**Event Data:**
```json
{
  "session_id": "string",
  "error_type": "processing_error",
  "error_message": "Failed to process audio chunk",
  "error_code": "AUDIO_PROCESSING_FAILED",
  "recovery_possible": false,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### Health Checks

#### GET /health
Socket service health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "service": "socket_service",
  "features": ["socket.io", "real_time_audio", "session_management"]
}
```

#### GET /health/redis
Redis connection health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "redis": "connected"
}
```

**Error Response (500):**
```json
{
  "status": "unhealthy",
  "redis": "error: connection failed"
}
```

---

## Management Service API

**Base Path:** `/management`

The Management Service handles all CRUD operations for tasks, submissions, scoring, and media.

### Task Sets Management

#### GET /task-sets/filtered
Get user's task sets with filtering and pagination.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 10, max: 100): Items per page
- `search` (string, optional): Search query
- `start_date` (datetime, optional): Start date for filtering
- `end_date` (datetime, optional): End date for filtering
- `status` (string, optional): Status filter - "PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"
- `sort_by` (string, default: "created_at"): Field to sort by
- `sort_order` (integer, default: -1): Sort order (1 for ascending, -1 for descending)
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": "string",
      "input_type": "audio",
      "input_content": "audio_file_url",
      "status": "COMPLETED",
      "total_score": 50,
      "scored": 45,
      "total_tasks": 5,
      "total_completed": 5,
      "created_at": "2024-01-01T10:00:00Z",
      "completed_at": "2024-01-01T10:15:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

#### GET /task-sets/{task_set_id}
Get a specific task set by ID.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_set_id` (string, required): Task set ID

**Query Parameters:**
- `include_tasks` (boolean, default: false): Include tasks in response
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
if  'include_tasks' is true
  ```json
  {
    "id": "string",
    "input_type": "audio",
    "input_content": "audio_file_url",
    "status": "COMPLETED",
    "total_score": 50,
    "scored": 45,
    "total_tasks": 5,
    "total_completed": 5,
    "created_at": "2024-01-01T10:00:00Z",
    "completed_at": "2024-01-01T10:15:00Z",
    "tasks": [
      {
        "id": "string",
        "type": "SINGLE_CHOICE",
        "question": {
          "text": "What is the capital of Nepal?",
          "text_english": "What is the capital of Nepal?"
        },
        "options": {
          "a": "Kathmandu",
          "b": "Pokhara",
          "c": "Lalitpur",
          "d": "Bhaktapur"
        },
        "correct_answer": {
          "selected_option": "a"
        },
        "user_answer": {
          "selected_option": "a"
        },
        "status": "COMPLETED",
        "result": "CORRECT",
        "total_score": 10,
        "scored": 10,
        "submitted": true,
        "submitted_at": "2024-01-01T10:05:00Z"
      }
    ]
}
else
 {
    "id": "string",
    "input_type": "audio",
    "input_content": "audio_file_url",
    "status": "COMPLETED",
    "total_score": 50,
    "scored": 45,
    "total_tasks": 5,
    "total_completed": 5,
    "created_at": "2024-01-01T10:00:00Z",
    "completed_at": "2024-01-01T10:15:00Z",
    "tasks": ["task_item_id_1", "task_item_id_2", "task_item_id_3", "task_item_id_4", "task_item_id_5"]
}
```

**Error Responses:**
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Server error

#### GET /task-sets/score/{task_set_id}
Get the score for a specific task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_set_id` (string, required): Task set ID

**Response (200 OK):**
```json
{
  "score": 45,
  "max_score": 50
}
```

**Error Responses:**
- `400 Bad Request`: Invalid task set ID
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Server error

### Task Items Management

#### GET /task-items/{task_id}
Get a specific task item by ID.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `task_id` (string, required): Task ID

**Query Parameters:**
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
{
  "id": "string",
  "type": "SINGLE_CHOICE",
  "question": {
    "text": "What is the capital of Nepal?",
    "text_english": "What is the capital of Nepal?"
  },
  "options": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Lalitpur",
    "d": "Bhaktapur"
  },
  "correct_answer": {
    "selected_option": "a"
  },
  "user_answer": {
    "selected_option": "a"
  },
  "status": "COMPLETED",
  "result": "CORRECT",
  "total_score": 10,
  "scored": 10,
  "submitted": true,
  "submitted_at": "2024-01-01T10:05:00Z",
  "attempts_count": 1
}
```

#### GET /task-items/set/{set_id}/tasks
Get all tasks for a specific task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `set_id` (string, required): Task set ID

**Query Parameters:**
- `fields` (array, optional): Fields to retrieve

**Response (200 OK):**
```json
[
  {
    "id": "string",
    "type": "SINGLE_CHOICE",
    "question": {
      "text": "What is the capital of Nepal?",
      "text_english": "What is the capital of Nepal?"
    },
    "options": {
      "a": "Kathmandu",
      "b": "Pokhara",
      "c": "Lalitpur",
      "d": "Bhaktapur"
    },
    "status": "PENDING",
    "result": null,
    "total_score": 10,
    "scored": 0
  }
]
```

### Submissions Management

#### POST /submissions/task-set
Submit answers for a complete task set.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "set_id": "string",           // required - task set ID
  "answers": [                  // required - array of answers
    {
      "task_id": "string",      // required - task ID
      "selected_option": "a"    // required - selected option for single/multiple choice
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_set_id": "string",
    "total_tasks": 5,
    "submitted_tasks": 5,
    "correct_answers": 4,
    "total_score": 50,
    "scored": 40,
    "accuracy": 80.0,
    "submission_time": "2024-01-01T10:15:00Z",
    "results": [
      {
        "task_id": "string",
        "result": "CORRECT",
        "scored": 10,
        "total_score": 10
      }
    ]
  },
  "message": "Task set submitted successfully",
  "meta": {
    "timestamp": "2024-01-01T10:15:00Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid task set ID or answers
- `404 Not Found`: Task set not found
- `500 Internal Server Error`: Submission failed

#### POST /submissions/task-item
Submit answer for a single task item.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "task_id": "string",          // required - task ID
  "selected_option": "a"        // required - selected option
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_id": "string",
    "result": "CORRECT",
    "scored": 10,
    "total_score": 10,
    "is_first_submission": true,
    "attempts_count": 1,
    "submission_time": "2024-01-01T10:05:00Z"
  },
  "message": "Task item submitted successfully",
  "meta": {
    "timestamp": "2024-01-01T10:05:00Z"
  }
}
```

### Scoring Management

#### GET /scoring/user/{user_id}
Get score for a specific user.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `user_id` (string, required): User ID

**Response (200 OK):**
```json
{
  "user_id": "string",
  "username": "string",
  "total_score": 450,
  "total_attempts": 15,
  "correct_answers": 12,
  "accuracy": 80.0,
  "last_attempt": "2024-01-01T10:15:00Z"
}
```

#### GET /scoring/user/current
Get current user's score.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "user_id": "string",
  "username": "string",
  "total_score": 450,
  "total_attempts": 15,
  "correct_answers": 12,
  "accuracy": 80.0,
  "last_attempt": "2024-01-01T10:15:00Z"
}
```

#### GET /scoring/leaderboard
Get leaderboard of top scoring users.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `limit` (integer, default: 10, max: 100): Maximum number of records
- `skip` (integer, default: 0): Number of records to skip (pagination)
- `sort_by` (string, default: "total_score"): Field to sort by - "total_score", "accuracy", "total_attempts"

**Response (200 OK):**
```json
{
  "data": [
    {
      "user_id": "string",
      "username": "string",
      "total_score": 850,
      "accuracy": 95.0,
      "total_attempts": 25,
      "rank": 1
    },
    {
      "user_id": "string",
      "username": "string",
      "total_score": 720,
      "accuracy": 88.0,
      "total_attempts": 20,
      "rank": 2
    }
  ],
  "meta": {
    "limit": 10,
    "skip": 0,
    "total_users": 150,
    "sort_by": "total_score"
  }
}
```

### Media Management

#### GET /media/file
Get presigned URL for a file.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `object_name` (string, required): Object name
- `folder` (string, required): Folder name

**Response (200 OK):**
```json
{
  "url": "https://storage.example.com/bucket/folder/file.mp3?signature=...",
  "object_name": "folder/file.mp3",
  "expires_in_hours": 24
}
```

#### POST /media/file
Get presigned URL via POST request.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "object_name": "string",      // required
  "folder": "string"            // required
}
```

**Response (200 OK):**
```json
{
  "url": "https://storage.example.com/bucket/folder/file.mp3?signature=...",
  "object_name": "folder/file.mp3",
  "expires_in_hours": 24
}
```

#### POST /media/upload
Upload a file to the server.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
- `file` (file, required): File to upload
- `folder` (string, default: "files"): Folder to save the file in

**Response (200 OK):**
```json
{
  "object_name": "abc123def4.mp3",
  "url": "",
  "content_type": "audio/mpeg",
  "size_bytes": 1024000,
  "original_filename": "recording.mp3",
  "folder": "recordings"
}
```

**Error Responses:**
- `400 Bad Request`: No file provided or invalid file
- `500 Internal Server Error`: Upload failed

### Health Check

#### GET /health
Management service health check.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "service": "management_service",
  "features": [
    "task_sets",
    "task_items",
    "submissions",
    "scoring",
    "media_handling",
    "leaderboards"
  ]
}
```

## Error Handling

All API endpoints follow consistent error response patterns. Errors are returned as JSON objects with appropriate HTTP status codes.

### Standard Error Response Format

```json
{
  "detail": "Error message describing the issue"
}
```

### API Response Format (Management Service)

The Management Service uses a standardized API response format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "meta": {
    "timestamp": "2024-01-01T10:00:00Z"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": "Additional error details"
  },
  "meta": {
    "timestamp": "2024-01-01T10:00:00Z"
  }
}
```

### Common Error Scenarios

#### Authentication Errors
- **401 Unauthorized**: Invalid or missing JWT token
- **403 Forbidden**: Insufficient permissions for the requested operation

#### Validation Errors
- **400 Bad Request**: Invalid request parameters, missing required fields, or malformed data
- **422 Unprocessable Entity**: Request data fails validation rules

#### Resource Errors
- **404 Not Found**: Requested resource does not exist
- **409 Conflict**: Resource already exists or conflicts with current state

#### Server Errors
- **500 Internal Server Error**: Unexpected server error
- **503 Service Unavailable**: Service temporarily unavailable

---

## Status Codes

### Success Codes

| Code | Description | Usage |
|------|-------------|-------|
| `200 OK` | Request successful | GET, PUT, DELETE operations |
| `201 Created` | Resource created successfully | POST operations that create resources |
| `204 No Content` | Request successful, no content to return | DELETE operations |

### Client Error Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| `400 Bad Request` | Invalid request | Missing required fields, invalid data format |
| `401 Unauthorized` | Authentication required | Missing or invalid JWT token |
| `403 Forbidden` | Access denied | Insufficient permissions, admin role required |
| `404 Not Found` | Resource not found | Invalid ID, resource doesn't exist |
| `409 Conflict` | Resource conflict | Duplicate username, role already exists |
| `422 Unprocessable Entity` | Validation failed | Data validation errors |
| `429 Too Many Requests` | Rate limit exceeded | Too many requests in time window |

### Server Error Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| `500 Internal Server Error` | Server error | Database connection issues, unexpected errors |
| `502 Bad Gateway` | Gateway error | Service communication issues |
| `503 Service Unavailable` | Service unavailable | Service maintenance, overload |
| `504 Gateway Timeout` | Gateway timeout | Service response timeout |

### Socket.IO Error Codes

| Code | Description | Recovery |
|------|-------------|----------|
| `AUDIO_PROCESSING_FAILED` | Audio processing error | Restart session |
| `SESSION_EXPIRED` | Session token expired | Create new session |
| `INVALID_SESSION` | Invalid session token | Authenticate again |
| `BUFFER_OVERFLOW` | Audio buffer overflow | Reduce chunk size |
| `PROCESSING_TIMEOUT` | Task generation timeout | Retry with shorter audio |

---

## Integration Examples

### Basic Authentication Flow

```bash
# 1. Register/Login
curl -X POST "http://napp-api.nextai.asia:8204/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123",
    "client_id": "your_client_id"
  }'

# Response: { "access_token": "eyJ...", "user": {...} }

# 2. Use token for protected endpoints
curl -X GET "http://napp-api.nextai.asia:8204/management/task-sets/filtered?page=1&limit=10" \
  -H "Authorization: Bearer eyJ..."
```

### Socket.IO Audio Streaming Flow

```javascript
// 1. Create session
const response = await fetch('/v1/socket/connect', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + jwt_token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    difficulty: 'medium',
    num_tasks: 5
  })
});

const session = await response.json();

// 2. Connect to Socket.IO
const socket = io('/v1/socket/socket.io', {
  auth: { session_token: session.session_token }
});

// 3. Start streaming
socket.emit('stream_starting', { session_id: session.session_id });

// 4. Send audio chunks
socket.emit('binary_data', audioChunk, {
  session_id: session.session_id,
  chunk_index: 1
});

// 5. Complete streaming
socket.emit('stream_completed', { session_id: session.session_id });
```

### Task Submission Flow

```bash
# 1. Get task set
curl -X GET "http://napp-api.nextai.asia:8204/v1/management/task-sets/task_set_id?include_tasks=true" \
  -H "Authorization: Bearer eyJ..."

# 2. Submit answers
curl -X POST "http://napp-api.nextai.asia:8204/v1/management/submissions/task-set" \
  -H "Authorization: Bearer eyJ..." \
  -H "Content-Type: application/json" \
  -d '{
    "set_id": "task_set_id",
    "answers": [
      {
        "task_id": "task_1_id",
        "selected_option": "a"
      }
    ]
  }'
```

---

## Rate Limiting

- **Authentication endpoints**: 10 requests per minute per IP
- **Socket.IO connections**: 5 concurrent sessions per user
- **File uploads**: 10 MB per file, 100 MB per hour per user
- **API requests**: 1000 requests per hour per authenticated user

---

## Support

For API support and questions:
- **Documentation**: Available at `http://napp-api.nextai.asia:8204/docs`
- **Health Checks**: Monitor service status via `/health` endpoints
- **Error Reporting**: Include request ID and timestamp in error reports

---

*This documentation is automatically updated with API changes. Last generated: December 2024*
# Audio Task API Documentation

This document outlines the API endpoints and flow for handling audio tasks in the application.

## Table of Contents
- [1. Upload Audio File](#1-upload-audio-file)
- [2. Submit Task Answer](#2-submit-task-answer)
- [3. Viewing Audio Responses](#3-viewing-audio-responses)
- [4. Frontend Implementation](#4-frontend-implementation)

---

## 1. Upload Audio File

Upload an audio file to the server for processing and storage.

### Request
```http
POST /management/media/upload
Content-Type: multipart/form-data
```

#### Form Data
| Parameter | Type     | Required | Description                          |
|-----------|----------|----------|--------------------------------------|
| file      | File     | Yes      | Audio file to upload (WAV/MP3 format)|
| folder    | String   | Yes      | Folder path to store the file        |

### Example Request

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    folder: str = Form("files", description="The folder to save the file in"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
```javascript
const formData = new FormData();
formData.append('file', audioBlob, 'recording.wav');
formData.append('folder', 'recordings');

const response = await fetch('/management/media/upload', {
  method: 'POST',
  body: formData
  // Headers are set automatically by the browser
});
```

### Successful Response (200 OK)
```json
{
  "success": true,
  "object_name": "recordings/abc123.wav",
  "file_name": "recording.wav",
  "content_type": "audio/wav",
  "size_bytes": 10240,
  "created_at": "2025-06-13T12:00:00Z"
}
```

### Error Responses
- `400 Bad Request`: Invalid file type or missing required fields
- `413 Payload Too Large`: File size exceeds limit
- `500 Internal Server Error`: Server error during upload

---

## 2. Submit Task Answer

Submit an answer for an audio task.

### Request
```http
POST /management/submissions/task-item
Content-Type: application/json
```

### Request Body
| Parameter | Type            | Required | Description                                      |
|-----------|-----------------|----------|--------------------------------------------------|
| task_id   | String          | Yes      | ID of the task being answered                    |
| answer    | String/Array    | Yes      | URL of the uploaded audio file                   |
| task_type | String          | Yes      | Type of task (e.g., 'speak_word')                |
| folder    | String          | Yes      | The object_name from the upload response         |


### Example Request
```javascript
const response = await fetch('/management/submissions/task-item', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    task_id: 'task_123',
    answer: 'https://storage.example.com/user_123/audio_responses/abc123.wav',
    task_type: 'speak_word',
    folder: 'user_123/audio_responses/abc123.wav'
  })
});
```

### Successful Response (200 OK)
```json
{
  "success": true,
  "data": {
    "task_id": "task_123",
    "is_correct": true,
    "scored": 10,
    "total_score": 10,
    "feedback": "Great job!"
  }
}
```

### Error Responses
- `400 Bad Request`: Missing required fields or invalid task ID
- `404 Not Found`: Task not found
- `422 Unprocessable Entity`: Validation error

---

## 3. Viewing Audio Responses

### Get Task Details
```http
GET /management/tasks/{task_id}
```

### Response Includes
```json
{
  "id": "task_123",
  "type": "speak_word",
  "question": {
    "text": "Record the word you hear",
    "answer_hint": "apple"
  },
  "user_answer": {
    "audio_url": "https://storage.example.com/user_123/audio_responses/abc123.wav",
    "submitted_at": "2025-06-13T12:05:00Z"
  },
  "status": "completed"
}
```

### Audio Player Implementation
```html
<audio controls>
  <source src="AUDIO_URL" type="audio/wav">
  Your browser does not support the audio element.
</audio>
```

---

## 4. Frontend Implementation

### Audio Recorder Component
```typescript
// Example React component for audio recording
function AudioRecorder({ onRecordingComplete }) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  
  const startRecording = async () => {
    // Implementation for starting recording
  };
  
  const stopRecording = () => {
    // Implementation for stopping recording
    onRecordingComplete(audioBlob);
  };
  
  return (
    <div>
      <button onClick={isRecording ? stopRecording : startRecording}>
        {isRecording ? 'Stop Recording' : 'Start Recording'}
      </button>
      {audioBlob && (
        <audio controls src={URL.createObjectURL(audioBlob)} />
      )}
    </div>
  );
}
```

### File Upload and Submission Flow
1. User records audio using the browser's MediaRecorder API
2. On stop, the audio blob is passed to the upload function
3. After successful upload, submit the task with the returned URL
4. Handle success/error responses and update UI accordingly

### Error Handling
- Show appropriate error messages for:
  - Microphone access denied
  - Upload failures
  - Network errors
  - Invalid file types/sizes

---

## Best Practices
1. **File Naming**: Use unique filenames (e.g., UUID + timestamp)
2. **File Size**: Implement client-side validation for file size
3. **Error Handling**: Provide clear feedback for all error cases
4. **Loading States**: Show loading indicators during upload/submission
5. **Retry Logic**: Allow retrying failed uploads
6. **Progress Tracking**: Show upload progress for large files

## Security Considerations
- Validate file types on both client and server
- Set appropriate CORS headers
- Implement rate limiting
- Sanitize all user input
- Use HTTPS for all file transfers

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

class SocketSession {
  final String sessionToken;
  final String sessionId;
  final String websocketUrl;
  final String expiresAt;
  final Map<String, dynamic> configuration;
  final String status;
  final Map<String, dynamic> instructions;

  SocketSession({
    required this.sessionToken,
    required this.sessionId,
    required this.websocketUrl,
    required this.expiresAt,
    required this.configuration,
    required this.status,
    required this.instructions,
  });

  factory SocketSession.fromJson(Map<String, dynamic> json) {
    return SocketSession(
      sessionToken: json['session_token'] ?? '',
      sessionId: json['session_id'] ?? '',
      websocketUrl: json['websocket_url'] ?? '',
      expiresAt: json['expires_at'] ?? '',
      configuration: json['configuration'] ?? {},
      status: json['status'] ?? '',
      instructions: json['instructions'] ?? {},
    );
  }
}

enum SocketConnectionState {
  disconnected,
  connecting,
  connected,
  active,
  completed,
  error,
  cancelled,
}

class SocketService {
  static final SocketService _instance = SocketService._internal();
  static SocketService get instance => _instance;

  io.Socket? _socket;
  SocketSession? _session;
  final Map<String, List<Function(dynamic)>> _eventHandlers = {};

  // State management
  final ValueNotifier<SocketConnectionState> connectionState = 
      ValueNotifier(SocketConnectionState.disconnected);
  final ValueNotifier<String?> errorMessage = ValueNotifier(null);

  SocketService._internal();

  /// Step 1: HTTP Authentication - Create session via HTTP
  /// This should be called from your HTTP service, not here
  /// Just keeping the interface for reference
  
  /// Step 2: Connect to WebSocket using session credentials
  Future<void> connectWebSocket(SocketSession session) async {
    _session = session;
    
    // Construct full WebSocket URL
    const baseUrl = 'https://napp-api.nextai.asia'; // Your API base URL
    final fullWebSocketUrl = '$baseUrl${session.websocketUrl}';
    
    debugPrint('🔗 Connecting to WebSocket: $fullWebSocketUrl');
    
    await _connect(session, fullWebSocketUrl);
  }

  /// Connect to socket with session
  Future<void> _connect(SocketSession session, String websocketUrl) async {
    // Disconnect existing socket
    if (_socket != null) {
      _socket!.disconnect();
      _socket = null;
    }

    // Parse URL for socket.io
    final uri = Uri.parse(websocketUrl);
    final socketBaseUrl = '${uri.scheme}://${uri.host}${uri.hasPort ? ':${uri.port}' : ''}';
    final path = uri.path;

    debugPrint('🔗 Socket Base URL: $socketBaseUrl');
    debugPrint('🔗 Socket Path: $path');

    final completer = Completer<void>();

    _socket = io.io(
      socketBaseUrl,
      io.OptionBuilder()
          .setPath(path)
          .setAuth({'session_token': session.sessionToken})
          .setTransports(['polling']) // ✅ ONLY polling transport
          .setTimeout(10000)
          .enableReconnection()
          .setReconnectionAttempts(3)
          .setReconnectionDelay(1000)
          .enableForceNew()
          .disableAutoConnect()
          .setExtraHeaders({
            'upgrade': 'false', // ✅ NO WebSocket upgrade
          })
          .build(),
    );

    _socket!.connect();

    _socket!.onConnect((_) {
      debugPrint('🔗 Socket connected');
      connectionState.value = SocketConnectionState.connected;
      _setupEventHandlers();
      _triggerEvent('state_change', {'status': 'CONNECTED'});
      if (!completer.isCompleted) completer.complete();
    });

    _socket!.onConnectError((error) {
      debugPrint('❌ Socket connection error: $error');
      connectionState.value = SocketConnectionState.error;
      errorMessage.value = error.toString();
      _triggerEvent('state_change', {'status': 'ERROR', 'error': error.toString()});
      if (!completer.isCompleted) {
        completer.completeError(Exception('Socket connection failed: $error'));
      }
    });

    _socket!.onDisconnect((reason) {
      debugPrint('🔌 Socket disconnected: $reason');
      connectionState.value = SocketConnectionState.disconnected;
      _triggerEvent('state_change', {'status': 'DISCONNECTED'});
      _triggerEvent('disconnect', {'reason': reason});
    });

    // Connection timeout
    Timer(const Duration(seconds: 10), () {
      if (!_socket!.connected && !completer.isCompleted) {
        completer.completeError(Exception('Socket connection timeout'));
      }
    });

    return completer.future;
  }

  /// Setup event handlers
  void _setupEventHandlers() {
    if (_socket == null) return;

    // Stream acknowledgments
    _socket!.on('stream_starting_ack', (data) {
      debugPrint('📡 Received stream_starting_ack: $data');
      connectionState.value = SocketConnectionState.active;
      _triggerEvent('stream_starting_ack', data);
    });

    _socket!.on('stream_completed_ack', (data) {
      debugPrint('📡 Received stream_completed_ack: $data');
      connectionState.value = SocketConnectionState.completed;
      _triggerEvent('stream_completed_ack', data);
    });

    _socket!.on('stream_stop_ack', (data) {
      debugPrint('📡 Received stream_stop_ack: $data');
      connectionState.value = SocketConnectionState.cancelled;
      _triggerEvent('stream_stop_ack', data);
    });

    // Task generation events
    _socket!.on('task_generation_processing', (data) {
      debugPrint('📡 Received task_generation_processing: $data');
      _triggerEvent('task_generation_processing', data);
    });

    _socket!.on('task_generation_complete', (data) {
      debugPrint('📡 Received task_generation_complete: $data');
      debugPrint('🎯 Task Set ID: ${data['task_set_id']}');
      connectionState.value = SocketConnectionState.completed;
      _triggerEvent('task_generation_complete', data);
    });

    _socket!.on('task_generation_failed', (data) {
      debugPrint('📡 Received task_generation_failed: $data');
      connectionState.value = SocketConnectionState.error;
      errorMessage.value = data['error']?.toString();
      _triggerEvent('task_generation_failed', data);
    });

    // Chunk acknowledgment
    _socket!.on('chunk_received', (data) {
      debugPrint('✅ Chunk received acknowledgment: $data');
      _triggerEvent('chunk_received', data);
    });

    // Error handling
    _socket!.on('error', (data) {
      debugPrint('❌ Server error: $data');
      connectionState.value = SocketConnectionState.error;
      errorMessage.value = data['message']?.toString();
      _triggerEvent('error', data);
    });
  }

  /// Start streaming
  Future<void> startStreaming() async {
    if (_socket == null || !_socket!.connected) {
      throw Exception('Socket not connected');
    }

    if (_session == null) {
      throw Exception('No session available');
    }

    final completer = Completer<void>();
    
    Timer(const Duration(seconds: 5), () {
      if (!completer.isCompleted) {
        completer.completeError(Exception('Stream starting timeout'));
      }
    });

    void handleStreamStartingAck(dynamic data) {
      if (!completer.isCompleted) {
        _socket!.off('stream_starting_ack');
        completer.complete();
      }
    }

    _socket!.on('stream_starting_ack', handleStreamStartingAck);
    _socket!.emit('stream_starting', {'session_id': _session!.sessionId});

    return completer.future;
  }

  /// Send audio chunk according to API specification
  void sendAudioChunk(Uint8List audioData, int chunkIndex) {
    if (_socket == null || !_socket!.connected) {
      throw Exception('Socket not connected');
    }

    if (_session == null) {
      throw Exception('No session available');
    }

    debugPrint('📤 Sending binary chunk #$chunkIndex: ${audioData.length} bytes');

    // Create payload object with all data and metadata as expected by server
    final payload = {
      'session_id': _session!.sessionId,
      'chunk_id': chunkIndex,
      'audio_data': audioData,
      'metadata': {
        'timestamp': DateTime.now().toIso8601String(),
        'chunk_size': audioData.length,
        'sequence_number': chunkIndex,
      }
    };

    debugPrint('📤 Sending binary payload: ${payload.keys}');

    // Send complete payload object as per API spec
    _socket!.emit('binary_data', payload);
  }

  /// Complete streaming - this triggers task generation but keeps socket alive
  void completeStreaming() {
    if (_socket == null || !_socket!.connected) {
      throw Exception('Socket not connected');
    }

    if (_session == null) {
      throw Exception('No session available');
    }

    final payload = {'session_id': _session!.sessionId};
    debugPrint('🏁 Sending stream_completed event with payload: $payload');

    // Emit the event - this will trigger task generation on the server
    _socket!.emit('stream_completed', payload);

    // Update state but keep socket connected to listen for task_generation_complete
    connectionState.value = SocketConnectionState.completed;

    debugPrint('✅ Stream completed, socket remains connected for task generation events');
  }

  /// Stop streaming
  void stopStreaming() {
    if (_socket == null || !_socket!.connected) {
      throw Exception('Socket not connected');
    }

    if (_session == null) {
      throw Exception('No session available');
    }

    final payload = {'session_id': _session!.sessionId};
    debugPrint('🛑 Sending stream_stop event with payload: $payload');
    _socket!.emit('stream_stop', payload);
  }

  /// Wait for task generation to complete
  Future<String> waitForTaskGeneration({int timeoutMs = 300000}) async {
    final completer = Completer<String>();
    
    Timer(Duration(milliseconds: timeoutMs), () {
      if (!completer.isCompleted) {
        completer.completeError(Exception('Task generation timeout'));
      }
    });

    void handleTaskGenerationComplete(dynamic data) {
      if (!completer.isCompleted) {
        off('task_generation_complete', handleTaskGenerationComplete);
        if (data['task_set_id'] != null) {
          completer.complete(data['task_set_id'].toString());
        } else {
          completer.completeError(Exception('No task_set_id received'));
        }
      }
    }

    void handleTaskGenerationFailed(dynamic data) {
      if (!completer.isCompleted) {
        off('task_generation_complete', handleTaskGenerationComplete);
        off('task_generation_failed', handleTaskGenerationFailed);
        completer.completeError(Exception(data['error']?.toString() ?? 'Task generation failed'));
      }
    }

    on('task_generation_complete', handleTaskGenerationComplete);
    on('task_generation_failed', handleTaskGenerationFailed);

    return completer.future;
  }

  /// Disconnect
  void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket = null;
    }
    _session = null;
    _eventHandlers.clear();
    connectionState.value = SocketConnectionState.disconnected;
  }

  /// Event handling
  void on(String event, Function(dynamic) handler) {
    _eventHandlers[event] ??= [];
    _eventHandlers[event]!.add(handler);
  }

  void off(String event, [Function(dynamic)? handler]) {
    if (!_eventHandlers.containsKey(event)) return;

    if (handler == null) {
      _eventHandlers.remove(event);
    } else {
      _eventHandlers[event]!.remove(handler);
    }
  }

  void _triggerEvent(String event, [dynamic data]) {
    if (_eventHandlers.containsKey(event)) {
      for (final handler in _eventHandlers[event]!) {
        try {
          handler(data);
        } catch (error) {
          debugPrint('Error in $event handler: $error');
        }
      }
    }
  }

  /// Getters
  bool get isConnected => _socket?.connected == true;
  SocketSession? get session => _session;
}

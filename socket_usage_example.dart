import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'socket_service.dart';

class AudioStreamingPage extends StatefulWidget {
  @override
  _AudioStreamingPageState createState() => _AudioStreamingPageState();
}

class _AudioStreamingPageState extends State<AudioStreamingPage> {
  final SocketService _socketService = SocketService.instance;
  bool _isStreaming = false;
  String _status = 'Disconnected';
  String? _taskSetId;

  @override
  void initState() {
    super.initState();
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    // Listen to connection state changes
    _socketService.connectionState.addListener(() {
      setState(() {
        _status = _socketService.connectionState.value.toString();
      });
    });

    // Listen to specific events
    _socketService.on('task_generation_complete', (data) {
      setState(() {
        _taskSetId = data['task_set_id']?.toString();
        _status = 'Task generation completed!';
      });
      
      // Navigate to task completion page or show success dialog
      _showTaskCompletionDialog();
    });

    _socketService.on('task_generation_failed', (data) {
      setState(() {
        _status = 'Task generation failed: ${data['error']}';
      });
    });

    _socketService.on('chunk_received', (data) {
      // Handle chunk acknowledgment
      print('Chunk received: ${data['chunk_id']}');
    });
  }

  Future<void> _connectToSocket() async {
    try {
      setState(() {
        _status = 'Connecting...';
      });

      // First, you need to get the session from your HTTP API
      // This is just an example - replace with your actual HTTP call
      final session = await _getSessionFromAPI();
      
      // Connect to WebSocket using the session
      await _socketService.connectWebSocket(session);
      
      setState(() {
        _status = 'Connected';
      });
    } catch (e) {
      setState(() {
        _status = 'Connection failed: $e';
      });
    }
  }

  Future<SocketSession> _getSessionFromAPI() async {
    // Replace this with your actual HTTP API call
    // Example using your HTTP service:
    /*
    final response = await httpService.post('/socket/connect', {
      // your payload
    });
    return SocketSession.fromJson(response.data);
    */
    
    // Mock session for example
    return SocketSession(
      sessionToken: 'your_session_token',
      sessionId: 'your_session_id',
      websocketUrl: '/v1/socket/socket.io/',
      expiresAt: DateTime.now().add(Duration(hours: 1)).toIso8601String(),
      configuration: {},
      status: 'active',
      instructions: {},
    );
  }

  Future<void> _startStreaming() async {
    if (!_socketService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Not connected to socket')),
      );
      return;
    }

    try {
      setState(() {
        _status = 'Starting stream...';
      });

      await _socketService.startStreaming();
      
      setState(() {
        _isStreaming = true;
        _status = 'Streaming active';
      });

      // Start your audio recording here
      _startAudioRecording();
      
    } catch (e) {
      setState(() {
        _status = 'Failed to start streaming: $e';
      });
    }
  }

  void _startAudioRecording() {
    // This is where you would integrate with your audio recording
    // For example, using flutter_sound or similar package
    
    // Simulate sending audio chunks
    int chunkIndex = 0;
    Timer.periodic(Duration(milliseconds: 100), (timer) {
      if (!_isStreaming) {
        timer.cancel();
        return;
      }

      // Replace this with actual audio data from your recorder
      final mockAudioData = Uint8List.fromList(
        List.generate(1024, (index) => (index % 256))
      );

      try {
        _socketService.sendAudioChunk(mockAudioData, chunkIndex++);
      } catch (e) {
        print('Error sending audio chunk: $e');
        timer.cancel();
      }

      // Stop after 10 seconds for demo
      if (chunkIndex > 100) {
        timer.cancel();
        _completeStreaming();
      }
    });
  }

  void _completeStreaming() {
    if (_isStreaming) {
      _socketService.completeStreaming();
      setState(() {
        _isStreaming = false;
        _status = 'Processing audio...';
      });
    }
  }

  void _stopStreaming() {
    if (_isStreaming) {
      _socketService.stopStreaming();
      setState(() {
        _isStreaming = false;
        _status = 'Stream stopped';
      });
    }
  }

  void _disconnect() {
    _socketService.disconnect();
    setState(() {
      _isStreaming = false;
      _status = 'Disconnected';
      _taskSetId = null;
    });
  }

  void _showTaskCompletionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Task Generation Complete!'),
        content: Text('Task Set ID: $_taskSetId'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to task set page
              // Navigator.pushNamed(context, '/tasks/$_taskSetId');
            },
            child: Text('View Tasks'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Audio Streaming'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text('Status: $_status'),
                    SizedBox(height: 8),
                    if (_taskSetId != null)
                      Text('Task Set ID: $_taskSetId'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _socketService.isConnected ? null : _connectToSocket,
              child: Text('Connect'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _socketService.isConnected && !_isStreaming 
                  ? _startStreaming 
                  : null,
              child: Text('Start Streaming'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isStreaming ? _completeStreaming : null,
              child: Text('Complete Streaming'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isStreaming ? _stopStreaming : null,
              child: Text('Stop Streaming'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _socketService.isConnected ? _disconnect : null,
              child: Text('Disconnect'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _socketService.disconnect();
    super.dispose();
  }
}

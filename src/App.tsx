import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './store'
import { ThemeProvider } from './services/theme/ThemeProvider'
import { NavigationProvider } from './contexts/NavigationContext'
import Login from './pages/Auth/Login'
import Signup from './pages/Auth/Signup'
import Onboarding from './pages/Onboarding/Onboarding'
import Dashboard from './pages/Dashboard/Dashboard'
import BeginLearning from './pages/BeginLearning/BeginLearning'
import Tasks from './pages/Tasks/Tasks'
import TaskSetDetail from './pages/Tasks/[tasksetid]/TaskSetDetail'
import TaskItem from './pages/Tasks/[tasksetid]/[taskitemid]/TaskItem'
import StoryItem from './pages/Tasks/[tasksetid]/storyitem/[storyitemid]/StoryItem'
import StoryLayout from './pages/Story/[story_id]/StoryLayout'
import StagePage from './pages/Story/[story_id]/[stage]/StagePage'
import ProtectedRoute from './components/ProtectedRoute'
import AuthInitializer from './components/AuthInitializer'
import GlobalStoryNotification from './components/common/GlobalStoryNotification'
import './styles/globals.css'

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <Router>
          <NavigationProvider>
            <AuthInitializer />
            <GlobalStoryNotification position="top-right" />
            <div className="min-h-screen bg-background text-foreground">
              <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />

              {/* Onboarding route - protected but accessible to new users */}
              <Route path="/onboarding" element={
                <ProtectedRoute>
                  <Onboarding />
                </ProtectedRoute>
              } />

              {/* Protected Routes */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } />

              <Route path="/begin-learning" element={
                <ProtectedRoute>
                  <BeginLearning />
                </ProtectedRoute>
              } />

              <Route path="/tasks" element={
                <ProtectedRoute>
                  <Tasks />
                </ProtectedRoute>
              } />

              <Route path="/tasks/:tasksetid" element={
                <ProtectedRoute>
                  <TaskSetDetail />
                </ProtectedRoute>
              } />

              <Route path="/tasks/:tasksetid/taskitem/:taskitemid" element={
                <ProtectedRoute>
                  <TaskItem />
                </ProtectedRoute>
              } />

              <Route path="/tasks/:tasksetid/storyitem/:storyitemid" element={
                <ProtectedRoute>
                  <StoryItem />
                </ProtectedRoute>
              } />



              <Route path="/story/:story_id" element={
                <ProtectedRoute>
                  <StoryLayout />
                </ProtectedRoute>
              } />

              {/* Default redirect */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </NavigationProvider>
        </Router>
      </ThemeProvider>
    </Provider>
  )
}

export default App

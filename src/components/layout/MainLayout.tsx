import React from 'react'
import { cn } from '../../utils/cn'
import SideNavigation from './SideNavigation'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
  topContent?: React.ReactNode
  bottomContent?: React.ReactNode
}

/**
 * Main layout component with sidebar navigation and structured content areas
 * Fully responsive across all device sizes
 */
const MainLayout: React.FC<MainLayoutProps> = React.memo(({
  children,
  title,
  description,
  className,
  topContent,
  bottomContent
}) => {
  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <SideNavigation />

      {/* Main Content */}
      <main className={cn(
        "flex-1 flex flex-col overflow-hidden",
        "min-w-0", // Prevent flex item from overflowing
        className
      )}>
        {/* Header - Always visible with title positioned after sidebar toggle */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
          <div className="flex h-10 sm:h-12 items-center px-2 sm:px-3 lg:px-4">
            {/* Title and description */}
            <div className="flex flex-col min-w-0 flex-1">
              {title && (
                <h1 className="text-base sm:text-lg font-semibold text-foreground truncate">
                  {title}
                </h1>
              )}
              {description && (
                <p className="text-xs text-muted-foreground truncate">
                  {description}
                </p>
              )}
            </div>
          </div>
        </header>

        {/* Top Content (Filters) */}
        {topContent && (
          <div className="border-b border-border bg-background flex-shrink-0">
            <div className="px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2">
              {topContent}
            </div>
          </div>
        )}

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-auto">
          <div className="px-2 sm:px-3 lg:px-4 py-2 sm:py-3">
            {children}
          </div>
        </div>

        {/* Bottom Content (Pagination) */}
        {bottomContent && (
          <div className="border-t border-border bg-background flex-shrink-0">
            <div className="px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2">
              {bottomContent}
            </div>
          </div>
        )}
      </main>
    </div>
  )
})

MainLayout.displayName = 'MainLayout'

export default MainLayout

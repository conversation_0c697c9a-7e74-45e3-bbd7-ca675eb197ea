import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import { 
  BookOpen, 
  X, 
  Loader2, 
  AlertCircle,
  Play,
  Clock,
  ChevronRight
} from 'lucide-react'
import { taskService } from '../../services/task/taskService'
import { storyService } from '../../services/story/storyService'
import { cn } from '../../utils/cn'

interface TaskSetStoriesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  taskSetId: string
  onStorySelect: (storyId: string) => void
}

interface StoryInfo {
  id: string
  title: string
  totalSteps: number
  completedSteps: number
  status: string
  createdAt: string
  firstStepContent?: string
}

/**
 * Modal for displaying and selecting stories related to a task set
 */
const TaskSetStoriesModal: React.FC<TaskSetStoriesModalProps> = ({
  open,
  onOpenChange,
  taskSetId,
  onStorySelect
}) => {
  const [stories, setStories] = useState<StoryInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch stories when modal opens
  useEffect(() => {
    if (open && taskSetId) {
      fetchStories()
    }
  }, [open, taskSetId])

  const fetchStories = async () => {
    try {
      setLoading(true)
      setError(null)

      // Get task set with story IDs
      const taskSetWithStories = await taskService.getTaskSetWithStoryIds(taskSetId)
      
      if (!taskSetWithStories.stories || taskSetWithStories.stories.length === 0) {
        setStories([])
        return
      }

      // Fetch details for each story
      const storyDetails = await Promise.all(
        taskSetWithStories.stories.map(async (storyId) => {
          try {
            const storyData = await storyService.getSingleStory(storyId, [
              'steps', 'total_steps', 'completed_steps', 'status', 'created_at', 'metadata', 'script', 'task_title'
            ])

            return {
              id: storyId,
              title: storyData.task_title || `Story ${stories.length + 1}`, // Use task_title from API
              totalSteps: storyData.total_steps || 1,
              completedSteps: storyData.completed_steps || 0,
              status: storyData.status || 'active',
              createdAt: storyData.created_at || new Date().toISOString(),
              firstStepContent: storyData.script || storyData.steps?.[0]?.content || storyData.steps?.[0]?.script
            }
          } catch (error) {
            console.error(`Failed to fetch story ${storyId}:`, error)
            return {
              id: storyId,
              title: `Story ${stories.length + 1}`,
              totalSteps: 1,
              completedSteps: 0,
              status: 'active',
              createdAt: new Date().toISOString()
            }
          }
        })
      )

      setStories(storyDetails)
    } catch (error) {
      console.error('Failed to fetch stories:', error)
      setError('Failed to load stories. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleStoryClick = (storyId: string) => {
    onStorySelect(storyId)
    onOpenChange(false)
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden max-h-[80vh] flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                    Available Stories
                  </h2>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Choose a story to continue your learning journey
                  </p>
                </div>
              </div>
              <Dialog.Close asChild>
                <button className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                  <X className="h-5 w-5 text-slate-500" />
                </button>
              </Dialog.Close>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex items-center gap-3 text-slate-600 dark:text-slate-400">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Loading stories...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                      Failed to Load Stories
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400 mb-4">{error}</p>
                    <button
                      onClick={fetchStories}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : stories.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <BookOpen className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                      No Stories Available
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      There are no stories available for this task set yet.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {stories.map((story, index) => (
                    <motion.div
                      key={story.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="cursor-pointer"
                      onClick={() => handleStoryClick(story.id)}
                    >
                      <div className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl p-4 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                              {index + 1}
                            </div>
                            <div>
                              <h3 className="font-medium text-slate-900 dark:text-white">
                                {story.title}
                              </h3>
                              <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                                <span className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatTimeAgo(story.createdAt)}
                                </span>
                                <span>{story.totalSteps} steps</span>
                                <span>{story.completedSteps}/{story.totalSteps} completed</span>
                              </div>
                            </div>
                          </div>
                          <ChevronRight className="h-5 w-5 text-slate-400" />
                        </div>
                        
                        {story.firstStepContent && (
                          <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                            {story.firstStepContent}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between mt-3">
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            story.status === 'completed' 
                              ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                              : "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                          )}>
                            {story.status === 'completed' ? 'Completed' : 'Active'}
                          </span>
                          
                          <button className="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                            <Play className="h-3 w-3" />
                            Start Reading
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default TaskSetStoriesModal

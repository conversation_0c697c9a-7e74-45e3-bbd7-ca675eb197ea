import React from 'react'
import { useTheme } from '../../services/theme/ThemeProvider'
import { AuthComponentProps, LoginFormData, SignupFormData } from './types'
import FormWrapper from './shared/FormWrapper'
import ModeSwitch from './shared/ModeSwitch'
import GoogleAuthComponent from './OAuth/Google/GoogleAuth.component'
import LoginComponent from './Login/Login.component'
import SignupComponent from './Signup/Signup.component'

/**
 * Auth Component - Main authentication component that renders login or signup
 */
const AuthComponent: React.FC<AuthComponentProps> = ({
  mode,
  formData,
  errors,
  isLoading,
  onModeChange,
  onInputChange,
  onSubmit,
  onGoogleAuth,
  onClearError,
}) => {
  const { theme } = useTheme()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 px-3 sm:px-4 md:px-6 lg:px-8">
      <FormWrapper>
        {/* Render Login or Signup Component */}
        {mode === 'login' ? (
          <LoginComponent
            formData={formData as LoginFormData}
            errors={errors}
            isLoading={isLoading}
            onInputChange={onInputChange}
            onSubmit={onSubmit}
            onClearError={onClearError}
          />
        ) : (
          <SignupComponent
            formData={formData as SignupFormData}
            errors={errors}
            isLoading={isLoading}
            onInputChange={onInputChange}
            onSubmit={onSubmit}
            onClearError={onClearError}
          />
        )}

        {/* Divider */}
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or
            </span>
          </div>
        </div>

        {/* Google Sign-in Button */}
        <div className="mb-4">
          <GoogleAuthComponent
            onSuccess={onGoogleAuth}
            onError={() => console.error('Google sign-in failed')}
            disabled={isLoading}
          />
        </div>

        {/* Mode Switch */}
        <div className="mt-4">
          <ModeSwitch
            mode={mode}
            onModeChange={onModeChange}
            disabled={isLoading}
          />
        </div>
      </FormWrapper>
    </div>
  )
}

export default AuthComponent

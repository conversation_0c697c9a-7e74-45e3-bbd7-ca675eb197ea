import React from 'react'
import { LoginComponentProps } from '../types'
import EmailOrUsernameInput from '../shared/EmailOrUsernameInput'
import PasswordInput from '../shared/PasswordInput'
import SubmitButton from '../shared/SubmitButton'

/**
 * Login Component - Pure UI component for login form
 */
const LoginComponent: React.FC<LoginComponentProps> = ({
  formData,
  errors,
  isLoading,
  onInputChange,
  onSubmit,
  onClearError,
}) => {
  // Form validation for submit button
  const isFormValid = () => {
    return formData.emailOrUsername?.trim() && formData.password?.trim()
  }

  return (
    <div>
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Welcome Back
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Sign in to your account to continue learning
        </p>
      </div>

      {/* General Error Display */}
      {errors.general && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
            <button
              onClick={onClearError}
              className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={onSubmit} className="space-y-6">
        {/* Email or Username field */}
        <EmailOrUsernameInput
          value={formData.emailOrUsername || ''}
          error={errors.emailOrUsername}
          onChange={(value) => onInputChange('emailOrUsername', value)}
          disabled={isLoading}
        />

        {/* Password field */}
        <PasswordInput
          value={formData.password || ''}
          error={errors.password}
          onChange={(value) => onInputChange('password', value)}
          disabled={isLoading}
          placeholder="Enter your password"
        />

        {/* Submit button */}
        <SubmitButton
          mode="login"
          isLoading={isLoading}
          disabled={isLoading || !isFormValid()}
        />
      </form>
    </div>
  )
}

export default LoginComponent

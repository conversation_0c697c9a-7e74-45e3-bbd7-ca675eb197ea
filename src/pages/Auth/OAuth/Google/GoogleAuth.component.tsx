import React from 'react'
import { GoogleLogin } from '@react-oauth/google'
import { GoogleAuthComponentProps } from '../../types'

/**
 * GoogleAuth Component - Google OAuth sign-in button component
 */
const GoogleAuthComponent: React.FC<GoogleAuthComponentProps> = ({
  onSuccess,
  onError,
  disabled = false,
}) => {
  const handleSuccess = (credentialResponse: any) => {
    if (credentialResponse.credential) {
      onSuccess(credentialResponse.credential)
    } else {
      onError()
    }
  }

  const handleError = () => {
    console.error('Google Sign-In failed')
    onError()
  }

  return (
    <div className="w-full">
      <GoogleLogin
        onSuccess={handleSuccess}
        onError={handleError}
        disabled={disabled}
        theme="outline"
        size="large"
        width="100%"
        text="signin_with"
        shape="rectangular"
      />
    </div>
  )
}

export default GoogleAuthComponent

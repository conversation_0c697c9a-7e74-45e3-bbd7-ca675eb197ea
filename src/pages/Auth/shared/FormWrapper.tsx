import React from 'react'

interface FormWrapperProps {
  children: React.ReactNode
}

/**
 * FormWrapper - Provides consistent styling and layout for auth forms
 */
const FormWrapper: React.FC<FormWrapperProps> = ({ children }) => {
  return (
    <div className="w-full max-w-md">
      <div className="bg-white dark:bg-gray-800 shadow-xl rounded-2xl px-8 py-10 border border-gray-200 dark:border-gray-700">
        {children}
      </div>
    </div>
  )
}

export default FormWrapper

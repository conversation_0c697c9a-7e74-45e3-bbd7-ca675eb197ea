import React from 'react'
import { SubmitButtonProps } from '../types'

/**
 * SubmitButton - Reusable submit button with loading state
 */
const SubmitButton: React.FC<SubmitButtonProps> = ({
  isLoading,
  disabled = false,
  mode,
}) => {
  return (
    <button
      type="submit"
      disabled={disabled}
      className={`
        w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-lg
        text-sm font-medium text-white bg-purple-600 hover:bg-purple-700
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-purple-600
        transition-colors duration-200
        ${isLoading ? 'cursor-wait' : ''}
      `}
    >
      {isLoading ? (
        <>
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {mode === 'signup' ? 'Creating account...' : 'Signing in...'}
        </>
      ) : (
        mode === 'signup' ? 'Create Account' : 'Sign In'
      )}
    </button>
  )
}

export default SubmitButton

import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import WelcomeSection from './sub-components/WelcomeSection'
import LearningStatsCard from './sub-components/LearningStatsCard'
import LeaderboardCard from './sub-components/LeaderboardCard'
import QuickAccessCard from './sub-components/QuickAccessCard'
import { DashboardComponentProps } from './types'

/**
 * Dashboard Component - Enhanced modern UI with improved information hierarchy
 */
const DashboardComponent: React.FC<DashboardComponentProps> = ({
  user,
  stats,
  statsLoading,
  statsError,
  onRefreshStats,
  leaderboard,
  leaderboardLoading,
  leaderboardError,
  onRefreshLeaderboard
}) => {
  // Minimal animation variants for sleek feel
  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Dashboard"
      description={`Welcome back, ${user.full_name || user.username}!`}
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="h-full w-full flex flex-col"
      >
        {/* Welcome Section - Full Width */}
        <div className="w-full mb-4">
          <WelcomeSection user={user} cardVariants={cardVariants} />
        </div>

        {/* Main Content Area - Two Column Layout */}
        <div className="flex-1 flex flex-col lg:flex-row gap-4 min-h-0">
          {/* Left Column - Stats and Quick Access */}
          <div className="lg:w-[75%] flex flex-col gap-4">
            {/* Learning Stats Card */}
            <div className="flex-none">
              <LearningStatsCard
                stats={stats}
                loading={statsLoading}
                error={statsError}
                onRefresh={onRefreshStats}
                cardVariants={cardVariants}
              />
            </div>
            
            {/* Quick Access Card */}
            <div className="flex-1">
              <QuickAccessCard cardVariants={cardVariants} />
            </div>
          </div>
          
          {/* Right Column - Full Height Leaderboard */}
          <div className="lg:w-[25%] flex-1 lg:flex-none">
            <div className="h-full">
              <LeaderboardCard
                leaderboard={leaderboard}
                loading={leaderboardLoading}
                error={leaderboardError}
                onRefresh={onRefreshLeaderboard}
                cardVariants={cardVariants}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </MainLayout>
  )
}

export default DashboardComponent

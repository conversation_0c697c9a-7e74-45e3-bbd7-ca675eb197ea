import React from 'react'
import { motion } from 'framer-motion'
import {
  RefreshCw,
  Target,
  BookOpen,
  CheckCircle,
  Loader2,
  Brain,
  Star
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { LearningStats } from '../../../services/stats/statsService'

interface LearningStatsCardProps {
  stats: LearningStats | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const LearningStatsCard: React.FC<LearningStatsCardProps> = ({
  stats,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  // Calculate derived metrics
  const completionRate = stats ? Math.round((stats.total_attempted_tasks / Math.max(stats.total_tasks, 1)) * 100) : 0
  const scorePercentage = stats ? Math.round((stats.total_scored / Math.max(stats.total_possible_score, 1)) * 100) : 0

  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden h-full flex flex-col"
    >
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center gap-2.5">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-sm">
            <Brain className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Learning Stats
            </h3>
            <p className="text-xs text-slate-500 dark:text-slate-400">
              Your performance metrics
            </p>
          </div>
        </div>
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            "p-2 rounded-lg transition-all duration-200",
            "bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700",
            "focus:outline-none focus:ring-2 focus:ring-blue-500",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <RefreshCw className={cn("h-4 w-4 text-slate-600 dark:text-slate-400", loading && "animate-spin")} />
        </button>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-3 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-red-600 dark:text-red-400 text-xs font-medium">{error}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-2" />
            <p className="text-slate-600 dark:text-slate-400 text-sm">Loading...</p>
          </div>
        </div>
      ) : (
        <div className="p-2 sm:p-3 flex-1 overflow-auto">
          {/* Compact Key Metrics Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            {/* Learning Sessions */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-700 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-purple-500 rounded-md">
                  <BookOpen className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">
                  Sessions
                </div>
              </div>
              <div className="flex items-baseline gap-1">
                <div className="text-xl font-bold text-purple-900 dark:text-purple-100">
                  {stats?.total_sets || 0}
                </div>
                <div className="text-xs text-purple-600 dark:text-purple-400">
                  total
                </div>
              </div>
            </div>

            {/* Tasks Completed */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-700 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-green-500 rounded-md">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-green-700 dark:text-green-300 font-medium">
                  Completed
                </div>
              </div>
              <div className="flex items-baseline gap-1">
                <div className="text-xl font-bold text-green-900 dark:text-green-100">
                  {stats?.total_attempted_tasks || 0}
                </div>
                <div className="text-xs text-green-600 dark:text-green-400">
                  tasks
                </div>
              </div>
            </div>

            {/* Accuracy Rate */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-blue-500 rounded-md">
                  <Target className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                  Accuracy
                </div>
              </div>
              <div className="flex items-baseline gap-1">
                <div className="text-xl font-bold text-blue-900 dark:text-blue-100">
                  {stats?.accuracy_rate || 0}
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400">
                  %
                </div>
              </div>
            </div>

            {/* Total Score */}
            <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border border-amber-200 dark:border-amber-700 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-amber-500 rounded-md">
                  <Star className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-amber-700 dark:text-amber-300 font-medium">
                  Points
                </div>
              </div>
              <div className="flex items-baseline gap-1">
                <div className="text-xl font-bold text-amber-900 dark:text-amber-100">
                  {stats?.total_scored || 0}
                </div>
                <div className="text-xs text-amber-600 dark:text-amber-400">
                  earned
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bars */}
          {stats && (
            <div className="mt-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-lg p-3 shadow-sm">
              <h4 className="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-3">
                Performance Metrics
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Completion Progress */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        Completion Rate
                      </span>
                    </div>
                    <span className="text-sm font-bold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-0.5 rounded">
                      {completionRate}%
                    </span>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-700 ease-out shadow-inner"
                      style={{ width: `${completionRate}%` }}
                    />
                  </div>
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1.5">
                    {stats.total_attempted_tasks} of {stats.total_tasks} tasks completed
                  </p>
                </div>

                {/* Score Performance */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        Score Rate
                      </span>
                    </div>
                    <span className="text-sm font-bold text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/30 px-2 py-0.5 rounded">
                      {scorePercentage}%
                    </span>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-amber-500 to-amber-600 h-2 rounded-full transition-all duration-700 ease-out shadow-inner"
                      style={{ width: `${scorePercentage}%` }}
                    />
                  </div>
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1.5">
                    {stats.total_scored} of {stats.total_possible_score} possible points
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </motion.div>
  )
}

export default LearningStatsCard

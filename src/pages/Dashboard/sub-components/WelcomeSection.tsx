import React from 'react'
import { motion } from 'framer-motion'
import { User } from '../../../types/auth'
import { Calendar, Clock, Award, Sparkles } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface WelcomeSectionProps {
  user: User
  cardVariants: any
}

const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  user,
  cardVariants
}) => {
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  // Get current date in a readable format
  const getCurrentDate = () => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
    return new Date().toLocaleDateString(undefined, options)
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-950/30 border border-blue-100 dark:border-indigo-900/50 rounded-xl p-3 sm:p-4"
    >
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-3">
        <div className="space-y-1">
          <div className="flex items-center gap-1.5">
            <Sparkles className="h-4 w-4 text-amber-500" />
            <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
              {getCurrentDate()}
            </span>
          </div>
          
          <h1 className="text-xl sm:text-2xl font-bold text-slate-900 dark:text-white flex items-center gap-1.5">
            {getGreeting()}, {user.full_name || user.username}!
            <span className="text-xl sm:text-2xl">👋</span>
          </h1>
          
          <p className="text-slate-600 dark:text-slate-300 text-sm">
            Ready to continue your Nepali learning journey today?
          </p>
        </div>

        <div className="flex items-center gap-3">
          {user.profile_picture ? (
            <div className="relative">
              <img
                src={user.profile_picture}
                alt={user.username}
                className="w-10 h-10 rounded-full border-2 border-white dark:border-slate-700 shadow-sm"
              />
              <div className="absolute -bottom-1 -right-1 bg-green-500 w-3 h-3 rounded-full border-2 border-white dark:border-slate-800"></div>
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold text-lg border-2 border-white dark:border-slate-700 shadow-sm">
              {(user.full_name || user.username).charAt(0).toUpperCase()}
            </div>
          )}
          
          <div className="hidden sm:block h-8 w-px bg-slate-200 dark:bg-slate-700"></div>
          
          <button className="hidden sm:flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-sm text-sm">
            <Award className="h-3.5 w-3.5" />
            <span className="font-medium">Start Learning</span>
          </button>
        </div>
      </div>
    </motion.div>
  )
}

export default WelcomeSection

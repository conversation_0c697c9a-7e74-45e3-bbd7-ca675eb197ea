import React, { useEffect, useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppSelector } from '../../store/hooks'
import { taskService } from '../../services/task/taskService'
import type { TaskSet, TaskSetFilterValues } from '../../services/task/taskService'
import TasksComponent from './Tasks.component'

// Filter interface for task sets
export interface TaskSetFilter {
  page: number
  limit: number
  sort_by: string
  sort_order: number
  status?: string
  input_type?: string
  source?: string
}

// Export TaskSet type for component
export type { TaskSet }

/**
 * Tasks Container - Handles logic and state for task sets list
 */
const TasksContainer: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [taskSets, setTaskSets] = useState<TaskSet[]>([])
  const [error, setError] = useState<string | null>(null)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [filterValues, setFilterValues] = useState<TaskSetFilterValues | null>(null)
  const [loadingFilters, setLoadingFilters] = useState(true)

  // Refs to prevent duplicate API calls
  const filterValuesLoadedRef = useRef(false)
  const taskSetsLoadingRef = useRef(false)

  // Filter state
  const [filter, setFilter] = useState<TaskSetFilter>({
    page: 1,
    limit: 12,
    sort_by: 'created_at',
    sort_order: -1
  })

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Cleanup refs on unmount
  useEffect(() => {
    return () => {
      filterValuesLoadedRef.current = false
      taskSetsLoadingRef.current = false
    }
  }, [])

  // Fetch filter values on component mount
  useEffect(() => {
    const fetchFilterValues = async () => {
      // Prevent duplicate calls
      if (filterValuesLoadedRef.current) return

      try {
        filterValuesLoadedRef.current = true
        setLoadingFilters(true)
        const values = await taskService.getTaskSetFilterValues({
          onError: (error) => {
            console.error('Error fetching filter values:', error)
            filterValuesLoadedRef.current = false // Reset on error
          }
        })
        setFilterValues(values)
      } catch (err) {
        console.error('Failed to fetch filter values:', err)
        filterValuesLoadedRef.current = false // Reset on error
      } finally {
        setLoadingFilters(false)
      }
    }

    if (isAuthenticated && user && !filterValuesLoadedRef.current) {
      fetchFilterValues()
    }
  }, [isAuthenticated, user])

  // Fetch task sets
  useEffect(() => {
    const fetchTaskSets = async () => {
      if (!user) return

      // Prevent duplicate calls for the same request
      if (taskSetsLoadingRef.current) return

      try {
        taskSetsLoadingRef.current = true
        setLoading(true)
        setError(null)

        // Use the real API call from reference
        const response = await taskService.fetchUserTaskSets(
          filter.page,
          filter.limit,
          filter.sort_by,
          filter.sort_order,
          filter.status,
          undefined, // search parameter
          undefined, // fields parameter
          {
            onError: (error: any) => {
              console.error('API Error:', error)
              setError(error.message || 'Failed to load task sets. Please try again.')
              taskSetsLoadingRef.current = false // Reset on error
            }
          }
        )

        setTaskSets(response.data)
        setTotalItems(response.meta.total)
        setTotalPages(Math.ceil(response.meta.total / filter.limit))
      } catch (err) {
        console.error('Error fetching task sets:', err)
        setError('Failed to load task sets. Please try again.')
      } finally {
        setLoading(false)
        taskSetsLoadingRef.current = false
      }
    }

    if (isAuthenticated && user) {
      fetchTaskSets()
    }
  }, [isAuthenticated, user, filter])

  // Handle filter changes
  const handleFilterChange = (newFilter: TaskSetFilter) => {
    setFilter(newFilter)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilter(prev => ({ ...prev, page }))
  }

  // Handle page size change
  const handlePageSizeChange = (limit: number) => {
    setFilter(prev => ({ ...prev, limit, page: 1 }))
  }

  // Navigate to task set details
  const handleTaskSetClick = (taskSetId: string) => {
    if (taskSetId) {
      navigate(`/tasks/${taskSetId}`)
    }
  }

  // Handle refresh
  const handleRefresh = () => {
    // Reset loading refs to allow fresh fetch
    filterValuesLoadedRef.current = false
    taskSetsLoadingRef.current = false

    // Trigger refetch by updating filter with timestamp
    setFilter(prev => ({ ...prev, _timestamp: Date.now() } as TaskSetFilter))
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <TasksComponent
      loading={loading}
      taskSets={taskSets}
      error={error}
      filter={filter}
      filterValues={filterValues}
      loadingFilters={loadingFilters}
      totalItems={totalItems}
      totalPages={totalPages}
      onFilterChange={handleFilterChange}
      onPageChange={handlePageChange}
      onPageSizeChange={handlePageSizeChange}
      onTaskSetClick={handleTaskSetClick}
      onRefresh={handleRefresh}
    />
  )
}

export default TasksContainer

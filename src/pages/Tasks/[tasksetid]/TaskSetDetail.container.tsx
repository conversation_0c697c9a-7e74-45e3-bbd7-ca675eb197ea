import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../../store/hooks'
import { taskService } from '../../../services/task/taskService'
import { useNavigation } from '../../../contexts/NavigationContext'
import TaskSetDetailComponent from './TaskSetDetail.component'
import {
  selectTaskSet,
  selectTaskSetScore,
  selectIsTaskSetCached,
  selectIsTaskSetScoreCached,
  selectIsCacheValid,
  setCurrentTaskSetId,
  setTaskSet,
  setTaskSetScore,
  setLoadingTaskSet,
  setLoadingTaskSetScore,
  setError,
  clearError,
  clearTaskSetCache
} from '../../../store/slices/taskSlice'



/**
 * TaskSetDetail Container - Handles logic and state for task set detail page
 */
const TaskSetDetailContainer: React.FC = () => {
  const { tasksetid } = useParams<{ tasksetid: string }>()
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { setBreadcrumbs, setCurrentTaskSet } = useNavigation()

  // Get data from Redux store
  const taskSet = useAppSelector((state) => tasksetid ? selectTaskSet(state, tasksetid) : null)
  const taskSetScore = useAppSelector((state) => tasksetid ? selectTaskSetScore(state, tasksetid) : null)
  const isTaskSetCached = useAppSelector((state) => tasksetid ? selectIsTaskSetCached(state, tasksetid) : false)
  const isTaskSetScoreCached = useAppSelector((state) => tasksetid ? selectIsTaskSetScoreCached(state, tasksetid) : false)
  const isCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `taskset_${tasksetid}`) : false)
  const isTaskSetScoreCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `score_${tasksetid}`) : false)
  const loading = useAppSelector((state) => state.task.loadingTaskSet)
  const loadingScore = useAppSelector((state) => tasksetid ? state.task.loadingTaskSetScore[tasksetid] || false : false)
  const error = useAppSelector((state) => state.task.error)



  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Fetch task set and task items based on URL with caching
  useEffect(() => {
    const fetchTaskSetData = async () => {
      if (!tasksetid || !user) return

      // Clear any previous errors
      dispatch(clearError())

      // Check if we have valid cached data
      if (isTaskSetCached && isCacheValid) {
        console.log('Using cached task set data')
        // No longer prefetch task questions - use lazy loading
        return
      }

      try {
        dispatch(setLoadingTaskSet(true))

        // Fetch task set details
        const fetchedTaskSet = await taskService.getTaskSet(tasksetid, false, false, {
          onError: (error: any) => {
            dispatch(setError(error.message))
          }
        })

        // Cache the task set in Redux
        dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))
        dispatch(setCurrentTaskSetId(tasksetid))

        // Fetch task set score if not cached or invalid
        if (!isTaskSetScoreCached || !isTaskSetScoreCacheValid) {
          dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
          try {
            const scoreData = await taskService.getTaskSetScore(tasksetid, {
              onError: (error) => {
                console.error('Error fetching task set score:', error)
              }
            })
            dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
          } catch (scoreError) {
            console.error('Failed to fetch task set score:', scoreError)
          } finally {
            dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
          }
        }

        // No longer prefetch task questions - use lazy loading instead

        // Update navigation context
        setCurrentTaskSet({ id: tasksetid, name: getTaskSetDisplayName(fetchedTaskSet) })
        setBreadcrumbs([
          {
            id: tasksetid,
            name: getTaskSetDisplayName(fetchedTaskSet),
            path: `/tasks/${tasksetid}`,
            type: 'taskset',
            isActive: true
          }
        ])

      } catch (err) {
        console.error('Error fetching task set data:', err)
        dispatch(setError('Failed to load task set. Please try again.'))
      } finally {
        dispatch(setLoadingTaskSet(false))
      }
    }

    if (isAuthenticated && user && tasksetid) {
      fetchTaskSetData()
    }
  }, [isAuthenticated, user, tasksetid, isTaskSetCached, isTaskSetScoreCached, isCacheValid, isTaskSetScoreCacheValid])

  // Helper function to get display name from input_content
  const getTaskSetDisplayName = (taskSet: any) => {
    if (!taskSet?.input_content) return 'Task Set'

    // If input_content is a string, return it
    if (typeof taskSet.input_content === 'string') {
      return taskSet.input_content
    }

    // If input_content is an object, extract meaningful name
    if (typeof taskSet.input_content === 'object') {
      return taskSet.input_content.file_name ||
             taskSet.input_content.object_name ||
             'Audio Task Set'
    }

    return 'Task Set'
  }

  // Removed fetchTaskQuestions function - using lazy loading instead

  // Navigate directly to task item quiz (no modal)
  const handleTaskItemClick = (taskItemId: string, taskIndex: number) => {
    if (tasksetid && taskItemId) {
      navigate(`/tasks/${tasksetid}/taskitem/${taskItemId}`)
    }
  }



  // Retry tasks - clear cache and refresh
  const handleRetryAgain = () => {
    if (tasksetid) {
      dispatch(clearTaskSetCache(tasksetid))
      // Refresh the data to get fresh state
      handleRefresh()
    }
  }



  // Refresh data
  const handleRefresh = async () => {
    if (!tasksetid || !user) return

    try {
      dispatch(setLoadingTaskSet(true))
      dispatch(clearError())

      const fetchedTaskSet = await taskService.getTaskSet(tasksetid, false, false)

      // Update cache
      dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))

      // Also refresh the task set score
      dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
      try {
        const scoreData = await taskService.getTaskSetScore(tasksetid)
        dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
      } catch (scoreError) {
        console.error('Failed to refresh task set score:', scoreError)
      } finally {
        dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
      }

      // No longer prefetch task questions during refresh - use lazy loading

    } catch (err) {
      console.error('Error refreshing task set data:', err)
      dispatch(setError('Failed to refresh data. Please try again.'))
    } finally {
      dispatch(setLoadingTaskSet(false))
    }
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (!tasksetid) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Task Set</h2>
          <p className="text-muted-foreground">No task set ID provided.</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <TaskSetDetailComponent
        taskSetId={tasksetid}
        taskSet={taskSet}
        taskSetScore={taskSetScore}
        taskItems={undefined} // No longer prefetching task items
        loading={loading}
        loadingScore={loadingScore}
        error={error}
        onTaskItemClick={handleTaskItemClick}
        onRefresh={handleRefresh}
        onRetryAgain={handleRetryAgain}
      />
    </>
  )
}

export default TaskSetDetailContainer

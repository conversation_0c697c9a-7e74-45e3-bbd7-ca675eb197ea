import httpBase, { RequestCallbacks } from '../http/httpBase'

// Types based on reference API structure
export interface TaskSet {
  id?: string
  _id?: string // MongoDB ID from backend
  tasks: string[] // Array of task IDs (based on actual API response)
  stories: string[] // Array of story IDs (based on actual API response)
  created_at: string
  user_id?: string
  input_type?: string
  input_content?: string | {
    object_name?: string
    bucket_name?: string
    object_path?: string
    file_name?: string
    content_type?: string
    size_bytes?: number
    folder?: string
    session_id?: string
    created_at?: string
    file_extension?: string
    url?: string // The actual URL to access the file
  } | null
  status?: string
  total_score?: number
  scored?: number
  submitted_at?: string | null
  verified_at?: string | null
  completed_at?: string | null
  difficulty_level?: string | null
  created_by?: string | null
  verified_by?: string | null
  attempted_tasks?: number
  total_tasks?: number
  attempts_count?: number
  notes?: string | null
  remark?: string | null
  input_metadata?: {
    object_name: string
    folder: string
    bucket?: string
    content_type?: string
    size_bytes?: number
  }
  [key: string]: any // Allow for additional properties
}

export interface QuestionObject {
  text: string
  translated_text: string
  options: {
    a: string
    b: string
    c: string
    [key: string]: string // Allow for additional options like d, e, etc.
  }
  image_url?: string | null
  answer_hint: string
  media_url: string | null
}

export interface TaskStory {
  stage: number
  script: string
  image?: string
  media_url?: string
  metadata?: Record<string, any>
}

export interface Task {
  id?: string
  _id?: string // MongoDB ID format from backend
  type: string
  question?: string | QuestionObject // Support both old string format and new object format
  options?: string[] // Keep for backward compatibility
  answer?: string
  word?: string
  audio_hint_url?: string
  media_url?: string // Unified field for images, audio, video
  status?: string
  correct_answer?: {
    value: string
    type: string
  }
  user_answer?: string | string[] // Can be single key or array of keys
  result?: string // 'correct' or 'incorrect'
  total_score?: number
  scored?: number
  is_attempted?: boolean
  submitted?: boolean
  submitted_at?: string
  story?: TaskStory // Story associated with this task item
  [key: string]: any // Allow for additional properties
}

export interface TaskSetFilter {
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: number
  status?: string
  input_type?: string
  source?: string
}

export interface TaskSetListResponse {
  data: TaskSet[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface SubmissionRequest {
  set_id: string
  answers: Array<{
    task_id: string
    selected_option?: string
    selected_options?: string[]
    text_answer?: string
  }>
}

export interface SubmissionResponse {
  success: boolean
  data: {
    set_id: string
    total_score: number
    scored: number
    results: Array<{
      task_id: string
      result: 'CORRECT' | 'INCORRECT' | 'PARTIAL'
      scored: number
      total_score: number
    }>
  }
  message: string
}

export interface TaskItemSubmissionResponse {
  success: boolean
  message?: string
  error?: any
  meta?: any
  data?: {
    task_id: string
    is_correct: boolean
    correct_answer: {
      value: string
      display_text: string
    }
    scored: number
    total_score: number
    feedback: string
    is_already_completed: boolean
  }
  // Alternative flat structure (for backward compatibility)
  task_id?: string
  result?: 'CORRECT' | 'INCORRECT' | 'PARTIAL'
  scored?: number
  total_score?: number
  is_correct?: boolean
  correct_answer?: {
    value: string
    display_text: string
  }
  feedback?: string
  is_already_completed?: boolean
}

export interface TaskSetScoreResponse {
  scored: number
  total_score: number
  percentage: number
  total_tasks: number
  attempted_tasks: number
  status: string
}

export interface TaskSetFilterValues {
  status: string[]
  difficulty_level: number[]
  source: string[]
  sort_by: string[]
  sort_type: string[]
}

class TaskService {
  // Get filtered task sets with pagination
  async getTaskSets(
    filter: TaskSetFilter = {},
    callbacks?: RequestCallbacks<TaskSetListResponse>
  ): Promise<TaskSetListResponse> {
    const params = new URLSearchParams()

    if (filter.page) params.append('page', filter.page.toString())
    if (filter.limit) params.append('limit', filter.limit.toString())
    if (filter.sort_by) params.append('sort_by', filter.sort_by)
    if (filter.sort_order) params.append('sort_order', filter.sort_order.toString())
    if (filter.status) params.append('status', filter.status)
    if (filter.input_type) params.append('input_type', filter.input_type)
    if (filter.source) params.append('source', filter.source)

    const response = await httpBase.get<TaskSetListResponse>(
      `/management/task-sets/filtered?${params.toString()}`,
      {},
      callbacks
    )

    return response.data
  }

  // Fetch user's task sets with filtering and pagination (alias for getTaskSets)
  async fetchUserTaskSets(
    page: number = 1,
    limit: number = 10,
    sortBy: string = "created_at",
    sortOrder: number = -1,
    status?: string,
    _search?: string,
    _fields?: string,
    callbacks?: RequestCallbacks<TaskSetListResponse>
  ): Promise<TaskSetListResponse> {
    const filter: TaskSetFilter = {
      page,
      limit,
      sort_by: sortBy,
      sort_order: sortOrder,
      status,
      input_type: undefined,
      source: undefined
    }

    return this.getTaskSets(filter, callbacks)
  }

  // Get a specific task set by ID
  async getTaskSet(
    taskSetId: string,
    includeTasks: boolean = false,
    includeStories: boolean = false,
    callbacks?: RequestCallbacks<TaskSet>
  ): Promise<TaskSet> {
    const params = new URLSearchParams()
    // if (includeTasks) params.append('include_tasks', includeTasks.toString())
    // if (includeStories) params.append('include_stories', includeStories.toString())

    const response = await httpBase.get<TaskSet>(
      `/management/task-sets/${taskSetId}?${params.toString()}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get task set with story IDs (include_stories=false returns just IDs)
  async getTaskSetWithStoryIds(
    taskSetId: string,
    callbacks?: RequestCallbacks<TaskSet & { stories?: string[] }>
  ): Promise<TaskSet & { stories?: string[] }> {
    const params = new URLSearchParams()
    params.append('include_tasks', 'false')
    params.append('include_stories', 'false')

    const response = await httpBase.get<TaskSet & { stories?: string[] }>(
      `/management/task-sets/${taskSetId}?${params.toString()}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get all tasks for a specific task set
  async getTaskItems(
    taskSetId: string,
    callbacks?: RequestCallbacks<Task[]>
  ): Promise<Task[]> {
    const response = await httpBase.get<Task[]>(
      `/management/task-items/set/${taskSetId}/tasks`,
      {},
      callbacks
    )

    return response.data
  }

  // Get a specific task item by ID
  async getTaskItem(
    taskId: string,
    callbacks?: RequestCallbacks<Task>
  ): Promise<Task> {
    const response = await httpBase.get<Task>(
      `/management/task-items/${taskId}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get question data for a specific task item
  async getTaskQuestion(
    taskId: string,
    callbacks?: RequestCallbacks<{ 
      type: string;
      question: { text: string };
      total_score: number;
      scored: number;
      created_at: string;
      title?: string;
    }>
  ): Promise<{ 
    type: string;
    question: { text: string };
    total_score: number;
    scored: number;
    created_at: string;
    title?: string;
  }> {
    const response = await httpBase.get<{
      type: string;
      question: { text: string };
      total_score: number;
      scored: number;
      created_at: string;
      title?: string;
    }>(
      `/management/task-items/question/${taskId}`,
      {},
      callbacks
    )

    return response.data
  }

  // Submit answers for a complete task set
  async submitTaskSet(
    submission: SubmissionRequest,
    callbacks?: RequestCallbacks<SubmissionResponse>
  ): Promise<SubmissionResponse> {
    const response = await httpBase.post<SubmissionResponse>(
      '/management/submissions/task-set',
      submission,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )

    return response.data
  }

  // Submit answer for a single task item
  async submitTaskItem(
    taskId: string,
    answer: {
      answer: string | string[] // Single option key or array of option keys
      task_type: string // Task type: "single_choice", "multiple_choice", "image_identification", etc.
      folder?: string // Optional folder for audio responses
    },
    callbacks?: RequestCallbacks<TaskItemSubmissionResponse>
  ): Promise<TaskItemSubmissionResponse> {
    const response = await httpBase.post<TaskItemSubmissionResponse>(
      '/management/submissions/task-item',
      {
        task_id: taskId,
        answer: answer.answer,
        task_type: answer.task_type,
        folder: answer.folder || ""
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )

    return response.data
  }

  // Get score for a specific task set
  async getTaskSetScore(
    taskSetId: string,
    callbacks?: RequestCallbacks<TaskSetScoreResponse>
  ): Promise<TaskSetScoreResponse> {
    const response = await httpBase.get<TaskSetScoreResponse>(
      `/management/task-sets/score/${taskSetId}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get available filter values for task sets
  async getTaskSetFilterValues(
    callbacks?: RequestCallbacks<TaskSetFilterValues>
  ): Promise<TaskSetFilterValues> {
    const response = await httpBase.get<TaskSetFilterValues>(
      '/management/task-sets/filters/values',
      {},
      callbacks
    )

    return response.data
  }
}

// Export singleton instance
export const taskService = new TaskService()
export default taskService

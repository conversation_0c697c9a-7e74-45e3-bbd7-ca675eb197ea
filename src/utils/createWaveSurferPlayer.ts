import WaveSurfer from 'wavesurfer.js'

interface WaveSurferConfig {
  container: HTMLElement
  url: string
  height?: number
  waveColor?: string
  progressColor?: string
  cursorColor?: string
  mediaControls?: boolean
  normalize?: boolean
  autoScroll?: boolean
  autoCenter?: boolean
  sampleRate?: number
  hideScrollbar?: boolean
}

/**
 * Creates and returns a WaveSurfer player instance for the given URL
 * This function directly returns the player instance, not using props
 */
export const createWaveSurferPlayer = (config: WaveSurferConfig): WaveSurfer => {
  const {
    container,
    url,
    height = 128,
    waveColor = '#e2e8f0',
    progressColor = '#dd5e98',
    cursorColor = '#ddd5e9',
    mediaControls = true,
    normalize = true,
    autoScroll = true,
    autoCenter = true,
    sampleRate = 8000,
    hideScrollbar = false
  } = config

  const wavesurfer = WaveSurfer.create({
    container,
    height,
    waveColor,
    progressColor,
    cursorColor,
    url,
    mediaControls,
    normalize,
    autoScroll,
    autoCenter,
    sampleRate,
    hideScrollbar,
  })

  // Set time to 10 seconds when ready (as per user preference)
  wavesurfer.on('ready', () => {
    wavesurfer.setTime(10)
  })

  return wavesurfer
}

export default createWaveSurferPlayer

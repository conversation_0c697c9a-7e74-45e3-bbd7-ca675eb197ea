import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../store/hooks'
import BeginLearningComponent from './BeginLearning.component'
import { socketService, SocketSession, SocketConnectionState } from '../../services/socket/socketService'
import {
  toggleStoryMode,
  setLastRecordedAudio,
  generateStory,
  clearGenerationError
} from '../../store/slices/storySlice'

interface BeginLearningState {
  connectionState: SocketConnectionState['status']
  session: SocketSession | null
  isRecording: boolean
  audioChunks: Blob[]
  error: string | null
  isLoading: boolean
  difficulty: 'easy' | 'medium' | 'hard'
  numTasks: number
  taskGenerationProgress: {
    processing: boolean
    completed: boolean
    taskSetId?: string
  }
  // Story mode specific state
  recordedAudioBlob: Blob | null
  isGeneratingStory: boolean
}

/**
 * Begin Learning Container - Handles socket connection and audio recording logic
 */
const BeginLearningContainer: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const isStoryModeEnabled = useAppSelector((state) => (state.story as any)?.isStoryModeEnabled || false)
  const storyGenerating = useAppSelector((state) => (state.story as any)?.isGenerating || false)
  const generationError = useAppSelector((state) => (state.story as any)?.generationError || null)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioStreamRef = useRef<MediaStream | null>(null)
  const chunkIndexRef = useRef(0)
  const recordedChunksRef = useRef<Blob[]>([])

  const [state, setState] = useState<BeginLearningState>({
    connectionState: 'DISCONNECTED',
    session: null,
    isRecording: false,
    audioChunks: [],
    error: null,
    isLoading: false,
    difficulty: 'easy',
    numTasks: 3,
    taskGenerationProgress: {
      processing: false,
      completed: false
    },
    recordedAudioBlob: null,
    isGeneratingStory: false
  })

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting, cleaning up socket connection')
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop()
      }
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop())
      }
      // Don't disconnect socket here - let it continue listening for task generation
    }
  }, [])

  // Setup socket event listeners
  useEffect(() => {
    const handleStateChange = (data: any) => {
      setState(prev => ({
        ...prev,
        connectionState: data.status,
        error: data.error || null
      }))
    }

    const handleChunkReceived = (data: any) => {
      console.log('✅ Chunk acknowledgment received:', data)
      // You can add chunk tracking state here if needed
    }

    const handleTaskGenerationProcessing = (_data: any) => {
      setState(prev => ({
        ...prev,
        taskGenerationProgress: {
          ...prev.taskGenerationProgress,
          processing: true
        }
      }))
    }

    const handleTaskGenerationComplete = (data: any) => {
      console.log('🎯 Task generation completed with data:', data)

      setState(prev => ({
        ...prev,
        taskGenerationProgress: {
          processing: false,
          completed: true,
          taskSetId: data.task_set_id
        }
      }))

      // Navigate to task set after completion
      if (data.task_set_id) {
        console.log(`🚀 Navigating to task set: ${data.task_set_id}`)
        setTimeout(() => {
          navigate(`/tasks/${data.task_set_id}`)
        }, 2000)
      } else {
        console.warn('⚠️ No task_set_id received in task_generation_complete event')
      }
    }

    const handleTaskGenerationFailed = (data: any) => {
      setState(prev => ({
        ...prev,
        error: data.error || 'Task generation failed',
        taskGenerationProgress: {
          processing: false,
          completed: false
        }
      }))
    }

    socketService.on('state_change', handleStateChange)
    socketService.on('chunk_received', handleChunkReceived)
    socketService.on('task_generation_processing', handleTaskGenerationProcessing)
    socketService.on('task_generation_complete', handleTaskGenerationComplete)
    socketService.on('task_generation_failed', handleTaskGenerationFailed)

    return () => {
      socketService.off('state_change', handleStateChange)
      socketService.off('chunk_received', handleChunkReceived)
      socketService.off('task_generation_processing', handleTaskGenerationProcessing)
      socketService.off('task_generation_complete', handleTaskGenerationComplete)
      socketService.off('task_generation_failed', handleTaskGenerationFailed)
    }
  }, [navigate])

  // Start recording - this will create session, connect, and start streaming
  const handleStartRecording = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Step 1: Create session and connect to WebSocket
      const session = await socketService.createSessionAndConnect()

      setState(prev => ({ ...prev, session, isLoading: false }))

      // Step 2: Start streaming on socket
      await socketService.startStreaming()

      // Step 3: Get microphone access and start recording
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      })

      audioStreamRef.current = stream

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      mediaRecorderRef.current = mediaRecorder
      chunkIndexRef.current = 0

      // Handle audio data
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log(`🎤 Audio chunk available: ${event.data.size} bytes, chunk #${chunkIndexRef.current}`)

          // Store chunks for story mode if enabled
          if (isStoryModeEnabled) {
            recordedChunksRef.current.push(event.data)
          }

          // Convert blob to array buffer and send
          event.data.arrayBuffer().then(arrayBuffer => {
            console.log(`🔄 Converting chunk #${chunkIndexRef.current} to ArrayBuffer: ${arrayBuffer.byteLength} bytes`)
            socketService.sendAudioChunk(arrayBuffer, chunkIndexRef.current++)
          }).catch(error => {
            console.error('❌ Error converting audio blob to ArrayBuffer:', error)
          })
        } else {
          console.warn('⚠️ Received empty audio chunk')
        }
      }

      // Add error handling for MediaRecorder
      mediaRecorder.onerror = (event) => {
        console.error('❌ MediaRecorder error:', event)
        setState(prev => ({
          ...prev,
          error: 'Audio recording error occurred',
          isRecording: false
        }))
      }

      mediaRecorder.onstop = () => {
        console.log('🛑 MediaRecorder stopped')

        // Create final audio blob for story mode if enabled and auto-generate story
        if (isStoryModeEnabled && recordedChunksRef.current.length > 0) {
          const audioBlob = new Blob(recordedChunksRef.current, { type: 'audio/webm' })
          setState(prev => ({ ...prev, recordedAudioBlob: audioBlob }))
          dispatch(setLastRecordedAudio(audioBlob))
          console.log('🎵 Audio blob created for story mode:', audioBlob.size, 'bytes')

          // Auto-generate story in parallel
          handleAutoGenerateStory(audioBlob)
        }
      }

      // Clear previous recorded chunks for story mode
      recordedChunksRef.current = []

      // Start recording with small chunks
      mediaRecorder.start(1000) // 1 second chunks
      console.log('🎤 MediaRecorder started with 1-second chunks')

      setState(prev => ({ ...prev, isRecording: true, recordedAudioBlob: null }))
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to start recording',
        isLoading: false
      }))
    }
  }, [state.difficulty, state.numTasks])



  // Stop recording
  const handleStopRecording = useCallback(async () => {
    try {
      console.log('🛑 Stopping recording and completing stream')

      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop()
      }

      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop())
        audioStreamRef.current = null
      }

      // Complete streaming on socket - this will trigger task generation
      await socketService.completeStreaming()

      console.log('✅ Stream completed, waiting for task generation...')

      setState(prev => ({
        ...prev,
        isRecording: false,
        taskGenerationProgress: {
          ...prev.taskGenerationProgress,
          processing: true
        }
      }))
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to stop recording',
        isRecording: false
      }))
    }
  }, [])

  // Disconnect
  const handleDisconnect = useCallback(() => {
    console.log('🔌 Disconnecting socket and cleaning up')

    // Stop recording if active
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop()
    }

    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop())
      audioStreamRef.current = null
    }

    socketService.disconnect()
    setState(prev => ({
      ...prev,
      connectionState: 'DISCONNECTED',
      session: null,
      isRecording: false,
      taskGenerationProgress: {
        processing: false,
        completed: false
      }
    }))
  }, [])

  // Update settings
  const handleSettingsChange = useCallback((settings: { difficulty: 'easy' | 'medium' | 'hard', numTasks: number }) => {
    setState(prev => ({
      ...prev,
      difficulty: settings.difficulty,
      numTasks: settings.numTasks
    }))
  }, [])

  // Clear error
  const handleClearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Auto-generate story function (called automatically when recording stops)
  const handleAutoGenerateStory = useCallback(async (audioBlob: Blob) => {
    try {
      setState(prev => ({ ...prev, isGeneratingStory: true }))
      console.log('🎬 Auto-generating story in parallel with task generation...')

      // Convert blob to file
      const audioFile = new File([audioBlob], 'recording.webm', {
        type: 'audio/webm'
      })

      // Generate story in parallel (don't await to not block task generation)
      dispatch(generateStory(audioFile))
        .unwrap()
        .then(() => {
          setState(prev => ({
            ...prev,
            isGeneratingStory: false,
            recordedAudioBlob: null // Clear after successful generation
          }))
          console.log('✅ Story generated successfully in parallel!')
        })
        .catch((error: any) => {
          setState(prev => ({
            ...prev,
            isGeneratingStory: false,
            error: `Story generation failed: ${error.message || 'Unknown error'}`
          }))
          console.error('❌ Story generation failed:', error)
        })

    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isGeneratingStory: false,
        error: `Story generation failed: ${error.message || 'Unknown error'}`
      }))
      console.error('❌ Story generation failed:', error)
    }
  }, [dispatch])

  // Story mode handlers
  const handleToggleStoryMode = useCallback(() => {
    dispatch(toggleStoryMode())
  }, [dispatch])

  // Manual story generation (for retry or manual trigger)
  const handleGenerateStory = useCallback(async () => {
    if (!state.recordedAudioBlob) {
      setState(prev => ({ ...prev, error: 'No audio recorded for story generation' }))
      return
    }

    await handleAutoGenerateStory(state.recordedAudioBlob)
  }, [state.recordedAudioBlob, handleAutoGenerateStory])

  const handleClearStoryError = useCallback(() => {
    dispatch(clearGenerationError())
  }, [dispatch])

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <BeginLearningComponent
      connectionState={state.connectionState}
      session={state.session}
      isRecording={state.isRecording}
      error={state.error}
      isLoading={state.isLoading}
      difficulty={state.difficulty}
      numTasks={state.numTasks}
      taskGenerationProgress={state.taskGenerationProgress}
      onStartRecording={handleStartRecording}
      onStopRecording={handleStopRecording}
      onDisconnect={handleDisconnect}
      onSettingsChange={handleSettingsChange}
      onClearError={handleClearError}
      // Story mode props
      isStoryModeEnabled={isStoryModeEnabled}
      recordedAudioBlob={state.recordedAudioBlob}
      isGeneratingStory={state.isGeneratingStory || storyGenerating}
      storyGenerationError={generationError}
      onToggleStoryMode={handleToggleStoryMode}
      onGenerateStory={handleGenerateStory}
      onClearStoryError={handleClearStoryError}
    />
  )
}

export default BeginLearningContainer
